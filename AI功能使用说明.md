# AI功能使用说明

## 🤖 AI助手功能

摸鱼背单词软件已集成AI助手，可以为每个单词提供：
- 记忆方法（词根词缀分析等）
- 近义词（相关同义词）
- 搭配用法（常见搭配）
- 例句（实际使用示例）

所有文字内容（单词、释义、AI回复）都使用统一的字体、字号、颜色和间距配置。

## 📋 控制台日志功能

程序会在控制台详细记录AI交互过程：
- 📤 发送给AI的消息（系统提示、用户输入、使用模型）
- 📥 AI的原始回复内容
- ✅ 操作成功状态
- ❌ 错误信息和重试过程

## ⌨️ AI相关快捷键

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `S` | 切换自动AI模式 | 开启后每次显示新单词时自动获取AI详情 |
| `A` | 手动获取AI详情 | 为当前单词获取AI辅助信息 |
| `D` | 显示/隐藏AI详情 | 切换AI详情区域的显示状态 |

## 🔄 使用流程

### 方式一：自动模式（推荐）
1. 按 `S` 键启用自动AI模式
2. 按 `N` 键切换单词，AI详情会自动获取并显示
3. 再次按 `S` 键可以禁用自动模式

### 方式二：手动模式
1. 按 `N` 键显示单词
2. 按 `A` 键手动获取当前单词的AI详情
3. 按 `D` 键可以隐藏/显示AI详情

## 📋 状态提示

程序会在控制台显示当前状态：
- `✅ 自动AI模式已启用` - 每次显示新单词时自动获取AI详情
- `❌ 自动AI模式已禁用` - 需要手动按 'A' 键获取AI详情
- `正在获取单词 'xxx' 的AI详情...` - AI API调用中
- `AI详情获取成功` - AI信息获取完成

## ⚙️ 配置说明

在 `word_config.json` 中可以配置：

```json
{
  "appearance": {
    "window": {
      "background_color": "#000000",    // 窗口背景色
      "normal_size": "320x200",         // 🔥 普通模式窗口大小
      "ai_mode_size": "400x350",        // 🔥 AI模式窗口大小
      "window_position": "+100+100"     // 窗口位置
    },
    "colors": {
      "text_color": "#ffffff"           // 🔥 统一文字颜色
    },
    "fonts": {
      "font_family": "Arial",           // 🔥 统一字体
      "font_size": 14,                  // 🔥 统一字号
      "font_weight": "bold"             // 🔥 统一字重
    },
    "spacing": {
      "padding_x": 8,                   // 🔥 统一水平间距
      "padding_y": 6                    // 🔥 统一垂直间距
    }
  },
  "ai_helper": {
    "enabled": true,                    // 是否启用AI功能
    "auto_fetch": true                  // 🔥 关键配置：是否自动获取AI详情
  }
}
```

### 🔥 重要配置说明

**窗口大小配置**：
- **`normal_size`** - 只显示单词和释义时的窗口大小
- **`ai_mode_size`** - 显示AI详情时的窗口大小

**统一文字样式**：
- 所有文字（单词、释义、AI回复）使用相同的字体、字号、颜色、间距
- 移除了所有图标和标题，保持简洁统一

**AI自动调用**：
- **`auto_fetch: true`** - 每次切换单词时自动调用AI获取详情
- **`auto_fetch: false`** - 需要手动按 `A` 键获取AI详情
- 通过 `S` 键可以实时切换此设置，并自动保存到配置文件

## 🚀 使用建议

1. **首次使用**：建议先按 `A` 键测试AI功能是否正常
2. **学习效率**：启用自动AI模式可以获得更丰富的学习信息
3. **网络环境**：AI功能需要网络连接，请确保网络畅通
4. **API配额**：注意API调用次数，避免超出配额限制

## 🔧 故障排除

1. **AI功能不可用**：
   - 检查配置文件中 `ai_helper.enabled` 是否为 `true`
   - 确认API密钥是否正确
   - 检查网络连接

2. **API调用失败**：
   - 检查API密钥权限
   - 确认模型名称是否正确
   - 查看控制台错误信息

3. **响应缓慢**：
   - 调整 `timeout` 设置
   - 检查网络延迟
   - 考虑使用缓存功能

## 💡 提示

- AI详情会自动缓存，相同单词不会重复调用API
- 可以通过 `D` 键随时切换AI详情的显示状态
- 自动AI模式下，切换单词时会自动隐藏上一个单词的AI详情
