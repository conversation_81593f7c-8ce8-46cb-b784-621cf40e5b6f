#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摸鱼背单词软件 - 终端版本
功能：在PowerShell终端中显示IELTS词汇，支持相同的快捷键和逻辑
与GUI版本共享相同的数据存储和学习进度
"""

import re
import random
import json
import os
import sys
import threading
import time
from datetime import datetime, timedelta

try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False

try:
    from ai_helper import AIHelper
    AI_HELPER_AVAILABLE = True
except ImportError:
    AI_HELPER_AVAILABLE = False

class TerminalAIHelper:
    """终端版本的AI助手包装器，显示调试信息"""
    def __init__(self, config):
        self.ai_helper = AIHelper(config)

    def get_word_details(self, word, pos=None, meaning=None):
        """获取单词详情，显示调试信息"""
        try:
            # 直接调用，不屏蔽调试信息
            result = self.ai_helper.get_word_details(word, pos, meaning)
            return result
        except Exception as e:
            print("❌ TerminalAIHelper异常: {}".format(e))
            return None

class WordTerminal:
    def __init__(self):
        """初始化终端版本的单词学习工具"""
        self.load_config()
        self.current_word_info = None
        self.current_ai_details = None
        self.ai_details_visible = False
        self.running = True

        # 从配置文件读取自动AI设置
        self.auto_ai_enabled = self.config.get('ai_helper', {}).get('auto_fetch', False)

        # 添加分步显示状态
        self.details_shown = False  # 是否已显示详细信息（词性+释义）

        # 显示模式：False=简洁模式（只显示单词），True=详细模式（显示单词+词性+释义）
        self.detailed_display_mode = False

        # 章节选择相关
        self.chapters = []
        self.current_chapter = None  # None表示全部单词，否则为章节索引
        self.chapter_selection_mode = False

        # 初始化核心组件
        self.setup_ai_helper()
        self.load_chapters()
        self.load_chapter_selection()  # 加载保存的章节选择
        self.load_words()
        self.load_progress()

        # 设置键盘监听
        self.setup_keyboard_listener()

        # 直接显示第一个单词
        self.show_next_word()

    def load_config(self):
        """加载配置文件（简洁版本）"""
        try:
            with open('word_config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            self.create_default_config()
        except Exception as e:
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置（与GUI版本相同）"""
        self.config = {
            "appearance": {
                "window": {
                    "background_color": "#000000",
                    "normal_size": "320x200",
                    "ai_mode_size": "400x350",
                    "window_position": "+100+100"
                },
                "colors": {
                    "ui_background": "#2a2a2a",
                    "text_background": "#333333",
                    "text_color": "#ffffff",
                    "border_color": "#444444"
                },
                "fonts": {
                    "font_family": "Arial",
                    "font_size": 14,
                    "font_weight": "bold"
                },
                "spacing": {
                    "padding_x": 8,
                    "padding_y": 6,
                    "margin_x": 5,
                    "margin_y": 3,
                    "border_width": 1
                }
            },
            "hotkeys": {
                "next_word": "n",
                "toggle_visibility": "h",
                "quit_app": "q",
                "quit_app_alt": "Escape",
                "familiar": "1",
                "vague": "2",
                "unfamiliar": "3",
                "clear_screen": "space"
            },
            "word_display": {
                "wrap_length": 280,
                "show_word_count": True,
                "auto_next_delay": 0
            },
            "ai_helper": {
                "enabled": False,
                "api_url": "https://api.dschat.asia/v1/chat/completions",
                "api_key": "",
                "model": "deepseek-ai/DeepSeek-V3",
                "max_retries": 3,
                "timeout": 10,
                "auto_fetch": False,
                "cache_enabled": True
            }
        }

    def setup_ai_helper(self):
        """设置AI助手（简洁版本）"""
        if not AI_HELPER_AVAILABLE:
            self.ai_helper = None
            return

        ai_config = self.config.get('ai_helper', {})

        if not ai_config.get('enabled', False):
            self.ai_helper = None
            return

        try:
            self.ai_helper = TerminalAIHelper(ai_config)
        except Exception as e:
            print("❌ AI助手初始化失败: {}".format(e))
            self.ai_helper = None

    def load_chapters(self):
        """加载章节信息"""
        self.chapters = []
        try:
            with open('IELTS阅读分类词汇.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 查找章节标题（格式：数字.数字 标题 或 数字. 数字 标题）
            chapter_pattern = r'^([0-9]+\.\s*[0-9]+)\s+(.+)$'

            for i, line in enumerate(lines):
                line = line.strip()
                match = re.match(chapter_pattern, line)
                if match:
                    chapter_num = match.group(1)
                    chapter_title = match.group(2)
                    self.chapters.append({
                        'number': chapter_num,
                        'title': chapter_title,
                        'line_number': i + 1
                    })

            print("📚 发现 {} 个章节".format(len(self.chapters)))

        except Exception as e:
            print("❌ 加载章节信息失败: {}".format(e))
            self.chapters = []

    def load_words(self):
        """从txt文件加载单词，支持章节过滤"""
        self.all_words = []  # 保存所有单词
        try:
            with open('IELTS阅读分类词汇.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 使用正则表达式匹配单词行
            word_pattern = r'^([a-zA-Z][a-zA-Z\s\-]*)\s+(n\.|adj\.|v\.|vt\.|vi\.|adv\.)'
            chapter_pattern = r'^([0-9]+\.\s*[0-9]+)\s+(.+)$'

            current_chapter_index = None

            for i, line in enumerate(lines):
                line = line.strip()

                # 检查是否是章节标题
                chapter_match = re.match(chapter_pattern, line)
                if chapter_match:
                    # 找到对应的章节索引
                    chapter_num = chapter_match.group(1)
                    for idx, chapter in enumerate(self.chapters):
                        if chapter['number'] == chapter_num:
                            current_chapter_index = idx
                            break
                    continue

                # 检查是否是单词行
                word_match = re.match(word_pattern, line)
                if word_match:
                    word = word_match.group(1).strip()
                    pos = word_match.group(2)  # 词性
                    # 提取中文释义
                    meaning = line[word_match.end():].strip()

                    word_info = {
                        'word': word,
                        'pos': pos,
                        'meaning': meaning,
                        'line_number': i + 1,
                        'chapter_index': current_chapter_index
                    }

                    self.all_words.append(word_info)

            # 根据当前章节设置过滤单词
            self.filter_words_by_chapter()

        except Exception as e:
            print("❌ 加载单词失败: {}".format(e))
            self.all_words = []
            self.words = []

    def filter_words_by_chapter(self):
        """根据当前章节过滤单词"""
        if self.current_chapter is None:
            # 显示所有单词
            self.words = self.all_words.copy()
        else:
            # 显示指定章节的单词
            self.words = [word for word in self.all_words
                         if word.get('chapter_index') == self.current_chapter]

        # 重置进度索引
        if hasattr(self, 'progress'):
            self.progress['current_index'] = 0

    def show_chapter_selection(self):
        """显示章节选择界面"""
        self.chapter_selection_mode = True
        self.clear_screen()

        print("📚 章节选择")
        print("="*50)
        print("0. 全部单词 ({} 个)".format(len(self.all_words)))

        for i, chapter in enumerate(self.chapters):
            # 计算该章节的单词数量
            chapter_words = [w for w in self.all_words if w.get('chapter_index') == i]
            word_count = len(chapter_words)

            marker = "✓" if self.current_chapter == i else " "
            print("{}. {} {} ({} 个单词)".format(
                i + 1, marker, chapter['number'] + " " + chapter['title'], word_count))

        print("="*50)
        print("请输入章节编号 (0-{}) 或按 'o' 键重新显示此菜单:".format(len(self.chapters)))

        if self.current_chapter is None:
            current_info = "全部单词"
        else:
            chapter = self.chapters[self.current_chapter]
            current_info = "{} {}".format(chapter['number'], chapter['title'])

        print("当前选择: {}".format(current_info))

    def select_chapter(self, chapter_input):
        """选择章节"""
        try:
            chapter_num = int(chapter_input)

            if chapter_num == 0:
                # 选择全部单词
                self.current_chapter = None
                self.save_chapter_selection()
                self.filter_words_by_chapter()
                self.chapter_selection_mode = False
                print("✅ 已选择: 全部单词 ({} 个)".format(len(self.words)))
                self.show_next_word()
                return True

            elif 1 <= chapter_num <= len(self.chapters):
                # 选择指定章节
                self.current_chapter = chapter_num - 1
                self.save_chapter_selection()
                self.filter_words_by_chapter()
                self.chapter_selection_mode = False

                chapter = self.chapters[self.current_chapter]
                print("✅ 已选择: {} {} ({} 个单词)".format(
                    chapter['number'], chapter['title'], len(self.words)))
                self.show_next_word()
                return True
            else:
                print("❌ 无效的章节编号，请输入 0-{}".format(len(self.chapters)))
                return False

        except ValueError:
            print("❌ 请输入有效的数字")
            return False

    def save_chapter_selection(self):
        """保存章节选择到配置文件"""
        try:
            # 添加章节选择到配置
            if 'learning' not in self.config:
                self.config['learning'] = {}

            self.config['learning']['current_chapter'] = self.current_chapter

            with open('word_config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print("❌ 保存章节选择失败: {}".format(e))

    def load_chapter_selection(self):
        """从配置文件加载章节选择"""
        try:
            learning_config = self.config.get('learning', {})
            saved_chapter = learning_config.get('current_chapter')

            if saved_chapter is not None and 0 <= saved_chapter < len(self.chapters):
                self.current_chapter = saved_chapter
            else:
                self.current_chapter = None

        except Exception as e:
            print("❌ 加载章节选择失败: {}".format(e))
            self.current_chapter = None

    def toggle_display_mode(self):
        """切换显示模式"""
        self.detailed_display_mode = not self.detailed_display_mode

        if self.detailed_display_mode:
            print("✅ 已切换到详细模式 - 默认显示单词+词性+释义")
        else:
            print("✅ 已切换到简洁模式 - 默认只显示单词，按'j'显示详情")

        # 重新显示当前单词
        if self.current_word_info:
            self.display_current_word()

    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def manual_clear_screen(self):
        """手动清屏并重新显示当前单词"""
        self.clear_screen()
        if self.current_word_info:
            self.display_current_word()

    def setup_keyboard_listener(self):
        """设置键盘监听"""
        if not PYNPUT_AVAILABLE:
            return

        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char:
                    if key.char == ' ':  # 空格键
                        self.handle_key_press('space')
                    else:
                        self.handle_key_press(key.char.lower())
            except AttributeError:
                # 处理特殊键
                if key == keyboard.Key.esc:
                    self.handle_key_press('escape')
                elif key == keyboard.Key.enter:
                    self.handle_key_press('return')
                elif key == keyboard.Key.space:
                    self.handle_key_press('space')

        self.listener = keyboard.Listener(on_press=on_press)
        self.listener.start()

    def handle_key_press(self, key):
        """处理按键事件"""
        hotkeys = self.config['hotkeys']

        # 如果在章节选择模式
        if self.chapter_selection_mode:
            if key == 'o':
                self.show_chapter_selection()
            elif key.isdigit():
                self.select_chapter(key)
            elif key == hotkeys['quit_app'] or key == 'escape':
                self.close_app()
            return

        # 正常模式的按键处理
        if key == 'o':
            self.show_chapter_selection()
        elif key == hotkeys['next_word']:
            self.show_next_word()
        elif key == hotkeys['quit_app'] or key == 'escape':
            self.close_app()
        elif key == hotkeys.get('get_ai_details', 'a'):
            self.get_ai_details()
        elif key == hotkeys.get('toggle_ai_details', 'd'):
            self.toggle_ai_details_display()
        elif key == hotkeys.get('toggle_auto_ai', 's'):
            self.toggle_auto_ai()
        elif key == hotkeys.get('familiar', '1'):
            self.rate_word_familiarity(2)  # 很熟悉
        elif key == hotkeys.get('vague', '2'):
            self.rate_word_familiarity(1)  # 有点模糊
        elif key == hotkeys.get('unfamiliar', '3'):
            self.rate_word_familiarity(0)  # 不认识
        elif key == hotkeys.get('clear_screen', 'space'):
            self.manual_clear_screen()
        elif key == hotkeys.get('show_details', 'j'):
            self.show_word_details()  # 显示详细信息
        elif key == 'u':
            self.toggle_display_mode()  # 切换显示模式

    def run(self):
        """运行终端版本"""
        try:
            if PYNPUT_AVAILABLE:
                while self.running:
                    time.sleep(0.1)
            else:
                # 如果没有pynput，使用输入模式
                self.run_input_mode()
        except KeyboardInterrupt:
            self.close_app()

    def run_input_mode(self):
        """输入模式运行（当pynput不可用时）"""
        while self.running:
            try:
                cmd = input().strip()
                if cmd == '':  # 空输入（Enter键）
                    self.handle_key_press('return')
                elif cmd == ' ':  # 空格键
                    self.handle_key_press('space')
                elif self.chapter_selection_mode and cmd.isdigit():
                    # 在章节选择模式下，支持多位数输入
                    self.select_chapter(cmd)
                elif cmd:
                    self.handle_key_press(cmd.lower())
            except (EOFError, KeyboardInterrupt):
                self.close_app()
                break

    def load_progress(self):
        """加载学习进度（与GUI版本完全相同）"""
        self.progress_file = 'word_progress.json'
        self.word_stats_file = 'word_stats.json'

        # 加载主进度文件
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    self.progress = json.load(f)
            else:
                self.progress = {
                    'current_index': 0,
                    'review_queue': [],
                    'last_review_date': None,
                    'total_learned': 0,
                    'new_words_since_review': 0,
                    'review_words_shown': 0,
                    'stats_summary': {
                        'familiar': 0,
                        'vague': 0,
                        'unfamiliar': 0
                    }
                }
        except Exception as e:
            self.progress = {
                'current_index': 0,
                'review_queue': [],
                'last_review_date': None,
                'total_learned': 0,
                'new_words_since_review': 0,
                'review_words_shown': 0,
                'stats_summary': {
                    'familiar': 0,
                    'vague': 0,
                    'unfamiliar': 0
                }
            }

        # 加载单词统计文件
        try:
            if os.path.exists(self.word_stats_file):
                with open(self.word_stats_file, 'r', encoding='utf-8') as f:
                    self.word_stats = json.load(f)
            else:
                self.word_stats = {}
        except Exception as e:
            self.word_stats = {}

        # 迁移旧格式数据
        self.migrate_old_progress_format()

    def migrate_old_progress_format(self):
        """迁移旧的进度格式到新格式（与GUI版本相同）"""
        # 检查是否有旧格式的word_stats在主进度文件中
        old_word_stats = self.progress.get('word_stats', {})
        if old_word_stats:
            # 迁移单词统计到独立文件
            for word, stats in old_word_stats.items():
                if 'familiarity_level' not in stats:
                    stats['familiarity_level'] = 0  # 默认为不熟悉
                if 'interval' not in stats:
                    stats['interval'] = 1  # 默认间隔1天
                if 'ease_factor' not in stats:
                    stats['ease_factor'] = 2.5  # 默认难度因子
                if 'next_review' not in stats:
                    # 如果有last_review，计算下次复习时间
                    if stats.get('last_review'):
                        last_review = datetime.fromisoformat(stats['last_review'])
                        next_review = last_review + timedelta(days=stats['interval'])
                        stats['next_review'] = next_review.isoformat()
                    else:
                        stats['next_review'] = datetime.now().isoformat()

                self.word_stats[word] = stats

            # 更新统计摘要
            self.update_stats_summary()

            # 从主进度文件中移除word_stats
            del self.progress['word_stats']

            # 保存迁移后的数据
            self.save_progress()
            self.save_word_stats()

        # 确保有必要的字段
        if 'review_queue' not in self.progress:
            self.progress['review_queue'] = []
        if 'last_review_date' not in self.progress:
            self.progress['last_review_date'] = None
        if 'total_learned' not in self.progress:
            self.progress['total_learned'] = len(self.word_stats)
        if 'new_words_since_review' not in self.progress:
            self.progress['new_words_since_review'] = 0
        if 'review_words_shown' not in self.progress:
            self.progress['review_words_shown'] = 0
        if 'stats_summary' not in self.progress:
            self.update_stats_summary()

    def update_stats_summary(self):
        """更新统计摘要（与GUI版本相同）"""
        familiar = vague = unfamiliar = 0
        for stats in self.word_stats.values():
            level = stats.get('familiarity_level', 0)
            if level == 2:
                familiar += 1
            elif level == 1:
                vague += 1
            else:
                unfamiliar += 1

        self.progress['stats_summary'] = {
            'familiar': familiar,
            'vague': vague,
            'unfamiliar': unfamiliar
        }
        self.progress['total_learned'] = len(self.word_stats)

    def save_progress(self):
        """保存学习进度（与GUI版本相同）"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            pass

    def save_word_stats(self):
        """保存单词统计数据（与GUI版本相同）"""
        try:
            with open(self.word_stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.word_stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            pass

    def print_learning_stats(self):
        """打印学习统计信息（与GUI版本相同的逻辑，但格式化为终端显示）"""
        total_words = len(self.words)
        learned_words = len(self.word_stats)

        # 从统计摘要获取数据
        stats_summary = self.progress.get('stats_summary', {'familiar': 0, 'vague': 0, 'unfamiliar': 0})
        familiar_count = stats_summary['familiar']
        vague_count = stats_summary['vague']
        unfamiliar_count = stats_summary['unfamiliar']

        # 更新复习队列以获取准确的待复习数量
        self.update_review_queue()
        review_count = len(self.progress['review_queue'])

        print("\n" + "="*50)
        print("📚 IELTS单词学习统计 - 终端版本")
        print("="*50)

        # 显示当前章节信息
        if self.current_chapter is None:
            print("当前章节: 全部单词")
        else:
            chapter = self.chapters[self.current_chapter]
            print("当前章节: {} {}".format(chapter['number'], chapter['title']))

        print("总词汇量: {} 个".format(total_words))
        print("已学习: {} 个 ({:.1f}%)".format(learned_words, learned_words/total_words*100 if total_words > 0 else 0))
        print("  - 很熟悉: {} 个".format(familiar_count))
        print("  - 有点模糊: {} 个".format(vague_count))
        print("  - 不认识: {} 个".format(unfamiliar_count))
        print("待复习: {} 个".format(review_count))
        print("="*50)
        print("快捷键说明:")
        print("  O - 章节选择")
        print("  U - 切换显示模式")
        print("  J - 显示详细信息（简洁模式下）")
        print("  1 - 标记为很熟悉")
        print("  2 - 标记为有点模糊")
        print("  3 - 标记为不认识")
        print("  N - 下一个单词")
        print("  Q - 退出程序")
        if self.ai_helper:
            print("  A - 获取AI详情")
            print("  S - 切换自动AI模式")
        print("="*50 + "\n")

    def calculate_next_review(self, word, familiarity_level):
        """计算下次复习时间（与GUI版本完全相同）"""
        stats = self.word_stats.get(word, {})
        current_interval = stats.get('interval', 1)
        ease_factor = stats.get('ease_factor', 2.5)

        # 根据熟悉度调整间隔和难度因子
        if familiarity_level == 0:  # 不认识
            new_interval = 1  # 重新开始
            ease_factor = max(1.3, ease_factor - 0.2)  # 降低难度因子
        elif familiarity_level == 1:  # 有点模糊
            new_interval = max(1, int(current_interval * 0.6))  # 缩短间隔
            ease_factor = max(1.3, ease_factor - 0.15)  # 稍微降低难度因子
        else:  # 很熟悉 (familiarity_level == 2)
            if current_interval == 1:
                new_interval = 6  # 第一次熟悉后6天
            elif current_interval < 6:
                new_interval = 6
            else:
                new_interval = int(current_interval * ease_factor)  # 按难度因子增长
            ease_factor = min(2.5, ease_factor + 0.1)  # 提高难度因子

        # 限制间隔范围
        new_interval = max(1, min(365, new_interval))  # 最短1天，最长1年

        # 计算下次复习时间
        next_review = datetime.now() + timedelta(days=new_interval)

        return new_interval, ease_factor, next_review

    def update_word_familiarity(self, word, familiarity_level):
        """更新单词熟悉度并计算下次复习时间（与GUI版本相同）"""
        if word not in self.word_stats:
            self.word_stats[word] = {
                'review_count': 0,
                'first_seen': datetime.now().isoformat(),
                'familiarity_level': familiarity_level,
                'interval': 1,
                'ease_factor': 2.5
            }

        stats = self.word_stats[word]
        stats['review_count'] += 1
        stats['last_review'] = datetime.now().isoformat()
        stats['familiarity_level'] = familiarity_level

        # 计算下次复习时间
        new_interval, new_ease_factor, next_review = self.calculate_next_review(word, familiarity_level)
        stats['interval'] = new_interval
        stats['ease_factor'] = new_ease_factor
        stats['next_review'] = next_review.isoformat()

        # 更新统计摘要
        self.update_stats_summary()

        # 更新复习队列
        self.update_review_queue()

        # 保存数据
        self.save_progress()
        self.save_word_stats()

        # 不打印反馈信息，保持简洁

    def update_review_queue(self):
        """更新复习队列（与GUI版本相同）"""
        now = datetime.now()
        review_queue = []

        for word, stats in self.word_stats.items():
            next_review_str = stats.get('next_review')
            if next_review_str:
                next_review = datetime.fromisoformat(next_review_str)
                if next_review <= now:  # 需要复习的单词
                    review_queue.append({
                        'word': word,
                        'next_review': next_review_str,
                        'priority': (now - next_review).total_seconds()  # 越久没复习优先级越高
                    })

        # 按优先级排序（优先级高的在前）
        review_queue.sort(key=lambda x: x['priority'], reverse=True)
        self.progress['review_queue'] = review_queue

    def rate_word_familiarity(self, familiarity_level):
        """评级当前单词的熟悉度并跳转到下一个单词（与GUI版本相同）"""
        if not self.current_word_info:
            return

        word = self.current_word_info['word']
        self.update_word_familiarity(word, familiarity_level)

        # 自动跳转到下一个单词
        self.show_next_word()

    def show_word_details(self):
        """显示当前单词的详细信息（词性+释义）- 仅在简洁模式下有效"""
        if not self.current_word_info:
            return

        # 如果已经是详细模式，不需要额外显示
        if self.detailed_display_mode:
            return

        if self.details_shown:
            return

        # 清屏并显示完整信息
        self.clear_screen()
        word_info = self.current_word_info
        print("单词: {} 词性: {} 释义: {}".format(
            word_info['word'],
            word_info['pos'],
            word_info['meaning']
        ))
        self.details_shown = True

    def show_next_word(self):
        """显示下一个单词（基于数量的重复逻辑）"""
        if not self.words:
            return

        # 基于数量的复习逻辑
        word_info = self.get_next_word_by_count()

        # 设置当前单词信息
        self.current_word_info = word_info

        # 重置详细信息显示状态
        self.details_shown = False

        # 隐藏之前的AI详情
        self.ai_details_visible = False
        self.current_ai_details = None

        # 清屏并显示单词信息
        self.clear_screen()
        self.display_current_word()

        # 保存进度
        self.save_progress()

    def get_next_word_by_count(self):
        """基于数量的单词选择逻辑"""
        # 配置参数
        NEW_WORDS_BEFORE_REVIEW = 5  # 每学5个新单词后复习旧单词
        REVIEW_WORDS_COUNT = 2       # 每次复习2个旧单词

        # 获取当前学习计数
        new_words_learned = self.progress.get('new_words_since_review', 0)
        review_words_shown = self.progress.get('review_words_shown', 0)

        # 判断是否需要复习旧单词
        if new_words_learned >= NEW_WORDS_BEFORE_REVIEW and review_words_shown < REVIEW_WORDS_COUNT:
            # 需要复习旧单词
            review_word = self.get_review_word()
            if review_word:
                self.progress['review_words_shown'] = review_words_shown + 1
                return review_word

        # 如果复习完成，重置计数器
        if review_words_shown >= REVIEW_WORDS_COUNT:
            self.progress['new_words_since_review'] = 0
            self.progress['review_words_shown'] = 0

        # 显示新单词
        current_index = self.progress.get('current_index', 0)

        # 确保索引在有效范围内
        if current_index >= len(self.words):
            current_index = 0

        word_info = self.words[current_index]
        self.progress['current_index'] = (current_index + 1) % len(self.words)

        # 如果这是一个新单词（之前没学过），增加新单词计数
        if word_info['word'] not in self.word_stats:
            self.progress['new_words_since_review'] = new_words_learned + 1

        return word_info

    def get_review_word(self):
        """获取需要复习的单词"""
        # 获取所有学过的单词，按熟悉度排序（不熟悉的优先）
        learned_words = []
        for word, stats in self.word_stats.items():
            familiarity = stats.get('familiarity_level', 0)
            review_count = stats.get('review_count', 0)
            learned_words.append({
                'word': word,
                'familiarity': familiarity,
                'review_count': review_count
            })

        if not learned_words:
            return None

        # 按熟悉度排序（不熟悉的在前），然后按复习次数排序（复习少的在前）
        learned_words.sort(key=lambda x: (x['familiarity'], x['review_count']))

        # 选择最需要复习的单词
        review_word_name = learned_words[0]['word']

        # 在单词列表中找到对应的单词信息
        for word in self.words:
            if word['word'] == review_word_name:
                return word

        return None

    def display_current_word(self):
        """显示当前单词信息，根据显示模式决定显示内容"""
        if not self.current_word_info:
            return

        word_info = self.current_word_info

        if self.detailed_display_mode:
            # 详细模式：显示单词+词性+释义
            print("单词: {} 词性: {} 释义: {}".format(
                word_info['word'],
                word_info['pos'],
                word_info['meaning']
            ))
        else:
            # 简洁模式：只显示单词
            print("单词: {}".format(word_info['word']))

    def get_ai_details(self):
        """获取当前单词的AI详情（简洁版本）"""
        if not self.ai_helper:
            return

        if not self.current_word_info:
            return

        word = self.current_word_info['word']
        pos = self.current_word_info['pos']
        meaning = self.current_word_info['meaning']

        print("正在调用AI API获取单词信息...")

        # 直接调用，不使用线程
        try:
            details = self.ai_helper.get_word_details(word, pos, meaning)
            self.update_ai_details(details)
        except Exception as e:
            print("❌ 获取AI详情失败: {}".format(e))
            self.update_ai_details(None)

    def update_ai_details(self, details):
        """更新AI详情显示（简洁版本）"""
        if details:
            self.current_ai_details = details
            self.ai_details_visible = True

            # 直接显示AI详情，不重新显示整个单词
            self.display_ai_details()

    def display_ai_details(self):
        """显示AI详情（简洁版本）"""
        if not self.current_ai_details:
            return

        details = self.current_ai_details
        raw_content = details.get('raw_content', '暂无详细信息')

        # 移除换行符，显示为连续文本
        display_text = raw_content.replace('\n', ' ').replace('\\n', ' ').strip()
        # 清理多余的空格
        display_text = ' '.join(display_text.split())

        print(display_text)

    def toggle_ai_details_display(self):
        """切换AI详情显示/隐藏"""
        if self.current_ai_details:
            self.ai_details_visible = not self.ai_details_visible
            if self.ai_details_visible:
                self.display_ai_details()
        else:
            self.get_ai_details()

    def toggle_auto_ai(self):
        """切换自动AI模式（与GUI版本相同）"""
        if not self.ai_helper:
            print("AI助手不可用，无法切换自动AI模式")
            return

        self.auto_ai_enabled = not self.auto_ai_enabled

        # 更新配置文件中的设置
        self.config['ai_helper']['auto_fetch'] = self.auto_ai_enabled
        self.save_config()

        if self.auto_ai_enabled:
            print("✅ 自动AI模式已启用 - 每次显示新单词时自动获取AI详情")
            print("配置已保存，下次启动时将保持此设置")
            # 如果当前有单词，立即获取AI详情
            if self.current_word_info:
                self.get_ai_details()
        else:
            print("❌ 自动AI模式已禁用 - 需要手动按 'A' 键获取AI详情")
            print("配置已保存，下次启动时将保持此设置")
            # 隐藏当前的AI详情
            self.ai_details_visible = False
            self.display_current_word()

    def save_config(self):
        """保存配置文件（与GUI版本相同）"""
        try:
            with open('word_config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print("配置文件已保存")
        except Exception as e:
            print("保存配置文件失败: {}".format(e))

    def close_app(self):
        """关闭应用"""
        self.save_progress()

        if PYNPUT_AVAILABLE and hasattr(self, 'listener'):
            self.listener.stop()

        self.running = False
        sys.exit(0)

if __name__ == "__main__":
    try:
        app = WordTerminal()
        app.run()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        pass
