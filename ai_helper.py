#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI助手模块 - 用于获取单词的记忆方法、近义词、搭配和例句
"""

import requests
import json
import time
from datetime import datetime

class AIHelper:
    def __init__(self, config):
        """初始化AI助手"""
        self.config = config
        self.api_url = config.get('api_url', 'https://api.dschat.asia/v1/chat/completions')
        self.api_key = config.get('api_key', '')
        self.model = config.get('model', 'deepseek-v3-250324-free')
        self.max_retries = config.get('max_retries', 3)
        self.timeout = config.get('timeout', 10)
        self.cache = {}  # 简单的内存缓存
        
    def get_word_details(self, word, pos=None, meaning=None):
        """获取单词详细信息"""
        # 检查缓存
        cache_key = word.lower()
        if cache_key in self.cache:
            return self.cache[cache_key]

        # 构建提示词
        prompt = self._build_prompt(word, pos, meaning)

        # 调用AI API
        try:
            response = self._call_ai_api(prompt)
            if response:
                # 缓存结果
                self.cache[cache_key] = response
                return response
            else:
                return self._get_fallback_response(word)
        except Exception as e:
            # 只在出错时显示调试信息
            self._show_error_debug_info(word, pos, meaning, e)
            return self._get_fallback_response(word)

    def _show_error_debug_info(self, word, pos, meaning, error):
        """显示错误调试信息"""
        print("❌ AI API调用失败，显示调试信息:")
        print("🔍 单词信息: {} ({}) - {}".format(word, pos or "未知", meaning or "未知"))
        print("📝 API配置:")
        print("   - API URL: {}".format(self.api_url))
        print("   - 模型: {}".format(self.model))
        print("   - API密钥: {}...{}".format(
            self.api_key[:8] if len(self.api_key) > 8 else "****",
            self.api_key[-4:] if len(self.api_key) > 4 else ""))
        print("   - 超时设置: {} 秒".format(self.timeout))
        print("   - 重试次数: {}".format(self.max_retries))
        print("❌ 错误信息: {}".format(error))
        print("   错误类型: {}".format(type(error).__name__))
        import traceback
        print("   详细堆栈:")
        traceback.print_exc()

    def _build_prompt(self, word, pos=None, meaning=None):
        """构建AI提示词"""
        base_prompt = "为了方便记忆，请补充这个单词的音标，记忆方法，近义词，搭配用法，例句，这个单词的其他意思，与这个单词外表很像的其他雅思常用词汇(并给出中文释义)，要求简练只给出必要的信息，最多不超过70个单词。"

        if pos and meaning:
            word_info = "单词: {} ({})\n释义: {}".format(word, pos, meaning)
        else:
            word_info = "单词: {}".format(word)

        return "{}\n\n{}".format(word_info, base_prompt)
    
    def _call_ai_api(self, prompt):
        """调用AI API"""
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer {}'.format(self.api_key)
        }

        system_message = '你是一个专业的英语学习助手，擅长帮助学生记忆单词。'

        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'system',
                    'content': system_message
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': 200,
            'temperature': 0.7
        }

        # 静默处理，不显示调试信息
        
        for attempt in range(self.max_retries):
            try:
                # 静默调用API
                
                response = requests.post(
                    self.api_url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']

                        # 静默处理AI回复

                        return self._parse_ai_response(content)
                    else:
                        print("❌ AI API返回格式错误")
                        print("响应内容: {}".format(result))
                        
                elif response.status_code == 429:
                    print("⚠️ API调用频率限制，等待后重试...")
                    time.sleep(2 ** attempt)  # 指数退避
                    continue

                else:
                    print("❌ AI API调用失败，状态码: {}".format(response.status_code))
                    print("响应内容: {}".format(response.text[:200]))
                    
            except requests.exceptions.Timeout as e:
                print("⏰ API调用超时: {} (尝试 {}/{})".format(e, attempt + 1, self.max_retries))
                print("   超时设置: {} 秒".format(self.timeout))
                if attempt < self.max_retries - 1:
                    print("   等待1秒后重试...")
                    time.sleep(1)
                continue

            except requests.exceptions.ConnectionError as e:
                print("🌐 网络连接错误: {} (尝试 {}/{})".format(e, attempt + 1, self.max_retries))
                print("   目标URL: {}".format(self.api_url))
                if attempt < self.max_retries - 1:
                    print("   等待1秒后重试...")
                    time.sleep(1)
                continue

            except requests.exceptions.HTTPError as e:
                print("🚫 HTTP错误: {} (尝试 {}/{})".format(e, attempt + 1, self.max_retries))
                if attempt < self.max_retries - 1:
                    print("   等待1秒后重试...")
                    time.sleep(1)
                continue

            except requests.exceptions.RequestException as e:
                print("❌ 网络请求异常: {} (尝试 {}/{})".format(e, attempt + 1, self.max_retries))
                print("   异常类型: {}".format(type(e).__name__))
                if attempt < self.max_retries - 1:
                    print("   等待1秒后重试...")
                    time.sleep(1)
                continue

            except ValueError as e:
                print("❌ JSON解析错误: {} (尝试 {}/{})".format(e, attempt + 1, self.max_retries))
                print("   响应可能不是有效的JSON格式")
                if attempt < self.max_retries - 1:
                    print("   等待1秒后重试...")
                    time.sleep(1)
                continue

            except Exception as e:
                print("❌ 未知异常: {} (尝试 {}/{})".format(e, attempt + 1, self.max_retries))
                print("   异常类型: {}".format(type(e).__name__))
                import traceback
                print("   详细错误信息:")
                traceback.print_exc()
                if attempt < self.max_retries - 1:
                    print("   等待1秒后重试...")
                    time.sleep(1)
                continue

        # 显示详细的失败调试信息
        self._show_api_failure_debug(prompt, data, headers)
        return None

    def _show_api_failure_debug(self, prompt, data, headers):
        """显示API调用失败的详细调试信息"""
        print("❌ 所有重试都失败了，显示详细调试信息:")
        print("📊 API配置:")
        print("   - API URL: {}".format(self.api_url))
        print("   - 模型: {}".format(self.model))
        print("   - 重试次数: {}".format(self.max_retries))
        print("   - 超时设置: {} 秒".format(self.timeout))
        print("   - API密钥: {}...{}".format(
            self.api_key[:8] if len(self.api_key) > 8 else "****",
            self.api_key[-4:] if len(self.api_key) > 4 else ""))

        print("📤 实际发送的API请求体:")
        print("   URL: {}".format(self.api_url))
        print("   Headers: {}".format({k: v if k != 'Authorization' else 'Bearer ***' for k, v in headers.items()}))
        print("   Request Body:")
        import json
        print("   {}".format(json.dumps(data, ensure_ascii=False, indent=4)))

        print("📝 提示词内容:")
        print("   {}".format(prompt))
    
    def _parse_ai_response(self, content):
        """直接返回AI响应的原始内容，不进行解析"""
        return {
            'raw_content': content
        }
    
    def _get_fallback_response(self, word):
        """获取备用响应（当AI API不可用时）"""
        return {
            'raw_content': '无法获取AI辅助信息，请检查网络连接或API配置。'
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        print("AI助手缓存已清空")
    
    def get_cache_size(self):
        """获取缓存大小"""
        return len(self.cache)

def test_ai_helper():
    """测试AI助手功能"""
    # 从配置文件读取设置
    try:
        import json
        with open('word_config.json', 'r', encoding='utf-8') as f:
            full_config = json.load(f)
        config = full_config.get('ai_helper', {})
    except:
        config = {
            'api_url': 'https://api.dschat.asia/v1/chat/completions',
            'api_key': 'sk-test',
            'model': 'gemini-2.0-flash-free',
            'max_retries': 3,
            'timeout': 20
        }

    ai_helper = AIHelper(config)

    # 测试单词
    test_word = "forecast"
    result = ai_helper.get_word_details(test_word, "vt.", "预测，预报，预兆")

    print("测试结果:")
    print("单词: {}".format(test_word))
    print("AI回复: {}".format(result['raw_content']))

def test_parse_response():
    """测试解析功能 - 验证原始内容保存"""
    ai_helper = AIHelper({})

    # 模拟AI回复
    mock_response = """记忆：fore(前) + cast(投掷) = 预先投掷 = 预测
近义词：predict, anticipate, foresee
搭配：weather forecast, forecast sales
例句：The weather forecast predicts rain tomorrow.
其他意思：n. 预报，预测
相似词汇：broadcast(广播), outcast(被驱逐者)"""

    result = ai_helper._parse_ai_response(mock_response)

    print("\n解析测试结果:")
    print("原始内容: {}".format(result['raw_content']))
    print("是否保持原始格式: {}".format(result['raw_content'] == mock_response))

    # 测试换行符处理
    print("\n换行符处理测试:")
    print("处理前（有换行）:")
    print(repr(result['raw_content']))

    # 模拟GUI/终端的处理逻辑
    display_text = result['raw_content'].replace('\n', ' ').replace('\\n', ' ').strip()
    display_text = ' '.join(display_text.split())

    print("处理后（无换行）:")
    print(repr(display_text))
    print("最终显示:")
    print(display_text)

if __name__ == "__main__":
    test_parse_response()
    print("\n" + "="*50 + "\n")
    test_ai_helper()
