# 间隔重复法功能说明

## 功能概述

我已经成功实现了基于间隔重复法（Spaced Repetition）的单词学习系统，替换了原有的简单时间记录系统。新系统能够根据用户对单词的熟悉程度智能安排复习时间，提高学习效率。

## 主要功能

### 1. 熟悉度评级系统
- **快捷键 1**: 标记为"很熟悉" (familiarity_level = 2)
- **快捷键 2**: 标记为"有点模糊" (familiarity_level = 1)  
- **快捷键 3**: 标记为"不认识" (familiarity_level = 0)

### 2. 间隔重复法算法
根据用户的熟悉度评级，系统会自动计算下次复习时间：

#### 很熟悉 (按键1)
- 第一次标记为熟悉：6天后复习
- 后续复习：按难度因子递增间隔
- 难度因子增加0.1（最高2.5）

#### 有点模糊 (按键2)
- 复习间隔缩短为当前间隔的60%
- 难度因子降低0.15（最低1.3）

#### 不认识 (按键3)
- 重新开始，1天后复习
- 难度因子降低0.2（最低1.3）

### 3. 智能复习队列
- 系统自动维护复习队列
- 优先显示需要复习的单词
- 按复习优先级排序（越久没复习优先级越高）

### 4. 进度数据结构
每个单词的学习数据包含：
```json
{
  "review_count": 复习次数,
  "last_review": "最后复习时间",
  "first_seen": "首次学习时间",
  "familiarity_level": 熟悉度等级(0-2),
  "interval": 复习间隔天数,
  "ease_factor": 难度因子(1.3-2.5),
  "next_review": "下次复习时间"
}
```

## 使用方法

### 基本操作
1. 启动程序后，会显示学习统计信息
2. 程序会自动选择需要复习的单词或新单词
3. 看到单词后，根据熟悉程度按相应按键：
   - **按键1**: 很熟悉这个单词
   - **按键2**: 有点印象但不太确定
   - **按键3**: 完全不认识
4. 系统会自动跳转到下一个单词并安排复习时间

### 学习模式
- **复习模式**: 优先显示到期需要复习的单词
- **学习模式**: 当没有需要复习的单词时，按顺序学习新单词

### 统计信息
程序启动时会显示：
- 总词汇量
- 已学习单词数量和百分比
- 各熟悉度等级的单词分布
- 待复习单词数量

## 算法优势

### 1. 科学性
基于艾宾浩斯遗忘曲线理论，在记忆即将遗忘时进行复习

### 2. 个性化
根据每个人对不同单词的掌握程度调整复习频率

### 3. 高效性
- 熟悉的单词复习间隔逐渐延长
- 困难的单词增加复习频率
- 避免重复学习已掌握的内容

### 4. 自适应
- 难度因子会根据学习表现动态调整
- 复习间隔在1天到1年之间自动调节

## 配置文件更新

在 `word_config.json` 中添加了新的快捷键配置：
```json
"hotkeys": {
  "familiar": "1",      // 标记为很熟悉
  "vague": "2",         // 标记为有点模糊  
  "unfamiliar": "3",    // 标记为不认识
  // ... 其他快捷键
}
```

## 数据迁移

系统会自动将旧的进度数据迁移到新格式：
- 保留原有的复习次数和时间记录
- 为所有已学习单词设置默认熟悉度为"不认识"
- 自动计算初始的复习间隔和难度因子

## 注意事项

1. **数据兼容性**: 系统完全兼容旧的进度文件，会自动迁移
2. **按键响应**: 确保窗口有焦点才能响应按键
3. **复习提醒**: 系统会在控制台显示详细的复习安排信息
4. **数据持久化**: 所有学习数据会自动保存到 `word_progress.json`

## 未来扩展

可以考虑添加的功能：
- 复习提醒通知
- 学习统计图表
- 导出学习报告
- 自定义复习算法参数
- 多用户支持

这个间隔重复法系统将大大提高你的单词学习效率，让你能够更科学地安排复习时间，确保长期记忆效果。
