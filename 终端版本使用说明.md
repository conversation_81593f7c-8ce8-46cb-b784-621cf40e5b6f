# IELTS单词学习工具 - 终端版本使用说明

## 概述

这是IELTS单词学习工具的终端版本，可以在PowerShell或命令行中运行。它与GUI版本（`word_flashcard.py`）共享相同的数据存储和学习进度，具有完全相同的功能逻辑。

## 特点

- ✅ **数据共享**：与GUI版本使用相同的进度文件和配置文件
- ✅ **功能完整**：支持间隔重复法、AI助手、熟悉度评级等所有功能
- ✅ **简洁显示**：终端界面简洁清晰，内容与弹窗版本一致
- ✅ **快捷键一致**：使用相同的快捷键操作
- ✅ **自动AI模式**：支持自动获取单词AI详情

## 运行方式

### 方法1：直接运行（推荐）
```bash
python word_terminal.py
```

### 方法2：如果pynput库不可用
程序会自动切换到输入模式，通过输入命令来操作。

## 快捷键说明

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `1` | 标记为很熟悉 | 自动跳转到下一个单词 |
| `2` | 标记为有点模糊 | 自动跳转到下一个单词 |
| `3` | 标记为不认识 | 自动跳转到下一个单词 |
| `N` | 下一个单词 | 显示下一个单词 |
| `A` | 获取AI详情 | 手动获取当前单词的AI记忆方法 |
| `S` | 切换自动AI模式 | 开启/关闭自动AI功能 |
| `Q` | 退出程序 | 保存进度并退出 |

## 界面说明

### 启动界面
```
==================================================
📚 IELTS单词学习统计 - 终端版本
==================================================
总词汇量: 3179 个
已学习: 81 个 (2.5%)
  - 很熟悉: 0 个
  - 有点模糊: 0 个
  - 不认识: 81 个
待复习: 0 个
==================================================
```

### 单词显示界面
```
============================================================
📚 IELTS单词学习 - 终端版本
============================================================
单词: cretaceous
词性: adj.
释义: [地]白垩纪的
------------------------------------------------------------
AI详情:
记忆：联想"create"(创造)+"aceous"(形容词后缀)，白垩纪是恐龙鼎盛时期。
近义：chalky（白垩的）
搭配：Cretaceous period（白垩纪）
例句：Fossils from the Cretaceous era reveal diverse dinosaur species.
------------------------------------------------------------
快捷键: [1]很熟悉 [2]有点模糊 [3]不认识 [N]下一个 [A]AI详情 [Q]退出
============================================================
```

## 功能特性

### 1. 间隔重复法
- 根据熟悉度自动调整复习间隔
- 优先显示需要复习的单词
- 智能的学习进度管理

### 2. AI助手功能
- 自动获取单词记忆方法、近义词、搭配和例句
- 支持手动和自动两种模式
- 使用DeepSeek-V3模型提供高质量内容

### 3. 学习统计
- 实时显示学习进度
- 分类统计熟悉度
- 复习队列管理

### 4. 数据持久化
- 自动保存学习进度
- 与GUI版本数据完全兼容
- 支持数据迁移和备份

## 配置文件

终端版本使用与GUI版本相同的配置文件 `word_config.json`，主要配置项：

```json
{
  "hotkeys": {
    "next_word": "n",
    "familiar": "1",
    "vague": "2", 
    "unfamiliar": "3",
    "quit_app": "q"
  },
  "ai_helper": {
    "enabled": true,
    "auto_fetch": true,
    "api_url": "https://api.dschat.asia/v1/chat/completions",
    "model": "deepseek-v3-250324-free"
  }
}
```

## 注意事项

1. **依赖库**：需要安装 `pynput` 库以支持全局快捷键
2. **AI功能**：需要配置有效的API密钥才能使用AI助手
3. **数据兼容**：可以与GUI版本交替使用，数据完全同步
4. **终端要求**：建议在支持UTF-8编码的终端中运行

## 故障排除

### 问题1：键盘监听不工作
**解决方案**：程序会自动切换到输入模式，通过输入命令操作

### 问题2：AI功能不可用
**解决方案**：检查配置文件中的API密钥和网络连接

### 问题3：中文显示乱码
**解决方案**：确保终端支持UTF-8编码

## 与GUI版本的区别

| 特性 | GUI版本 | 终端版本 |
|------|---------|----------|
| 显示方式 | 弹窗界面 | 终端输出 |
| 交互方式 | 鼠标+键盘 | 键盘快捷键 |
| 数据存储 | ✅ 相同 | ✅ 相同 |
| 功能逻辑 | ✅ 相同 | ✅ 相同 |
| AI助手 | ✅ 支持 | ✅ 支持 |
| 间隔重复 | ✅ 支持 | ✅ 支持 |

## 开发说明

终端版本的核心代码复用了GUI版本的以下模块：
- 单词加载和解析逻辑
- 进度管理和数据存储
- 间隔重复算法
- AI助手集成
- 配置文件管理

这确保了两个版本的功能完全一致，只是显示和交互方式不同。
