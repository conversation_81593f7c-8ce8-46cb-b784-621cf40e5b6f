#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摸鱼背单词软件 - 透明弹窗版本
功能：显示IELTS词汇，支持透明无边框弹窗
"""

import tkinter as tk
from tkinter import ttk
import re
import random
import json
import os
from datetime import datetime, timedelta
import threading
import math
try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("警告：pynput库未安装，全局快捷键功能将不可用")

try:
    from ai_helper import AIHelper
    AI_HELPER_AVAILABLE = True
except ImportError:
    AI_HELPER_AVAILABLE = False
    print("警告：ai_helper模块未找到，AI功能将不可用")

class WordFlashcard:
    def __init__(self):
        self.root = tk.Tk()
        self.load_config()
        self.is_visible = True
        self.ai_details_visible = False
        self.current_ai_details = None
        # 从配置文件读取自动AI设置
        self.auto_ai_enabled = self.config.get('ai_helper', {}).get('auto_fetch', False)
        # 添加分步显示状态
        self.details_shown = False  # 是否已显示详细信息（词性+释义）
        self.setup_ai_helper()
        self.setup_window()
        self.load_words()
        self.load_progress()
        self.current_word_info = None
        self.setup_ui()
        self.setup_global_hotkeys()
        self.print_learning_stats()
        self.show_next_word()

    def load_config(self):
        """加载配置文件"""
        try:
            with open('word_config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print("配置文件加载成功")
        except FileNotFoundError:
            print("配置文件不存在，使用默认配置")
            self.create_default_config()
        except Exception as e:
            print("加载配置文件失败：{}，使用默认配置".format(e))
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置"""
        self.config = {
            "appearance": {
                "window": {
                    "background_color": "#000000",
                    "normal_size": "320x200",
                    "ai_mode_size": "400x350",
                    "window_position": "+100+100"
                },
                "colors": {
                    "ui_background": "#2a2a2a",
                    "text_background": "#333333",
                    "text_color": "#ffffff",
                    "border_color": "#444444"
                },
                "fonts": {
                    "font_family": "Arial",
                    "font_size": 14,
                    "font_weight": "bold"
                },
                "spacing": {
                    "padding_x": 8,
                    "padding_y": 6,
                    "margin_x": 5,
                    "margin_y": 3,
                    "border_width": 1
                }
            },
            "hotkeys": {
                "next_word": "n",
                "toggle_visibility": "h",
                "quit_app": "q",
                "quit_app_alt": "Escape",
                "familiar": "1",
                "vague": "2",
                "unfamiliar": "3"
            },
            "word_display": {
                "wrap_length": 280,
                "show_word_count": True,
                "auto_next_delay": 0
            },
            "ai_helper": {
                "enabled": False,
                "api_url": "https://api.dschat.asia/v1/chat/completions",
                "api_key": "",
                "model": "deepseek-ai/DeepSeek-V3",
                "max_retries": 3,
                "timeout": 10,
                "auto_fetch": False,
                "cache_enabled": True
            }
        }

    def setup_ai_helper(self):
        """设置AI助手"""
        if not AI_HELPER_AVAILABLE:
            print("AI助手模块不可用")
            self.ai_helper = None
            return

        ai_config = self.config.get('ai_helper', {})
        if not ai_config.get('enabled', False):
            print("AI助手已禁用")
            self.ai_helper = None
            return

        try:
            self.ai_helper = AIHelper(ai_config)
            print("AI助手初始化成功")

            # 显示自动AI模式状态
            if self.auto_ai_enabled:
                print("✅ 自动AI模式已启用 - 每次显示新单词时自动获取AI详情")
            else:
                print("❌ 自动AI模式已禁用 - 需要手动按 'A' 键获取AI详情")

        except Exception as e:
            print("AI助手初始化失败: {}".format(e))
            self.ai_helper = None
        
    def setup_window(self):
        """设置无边框窗口"""
        # 设置窗口属性
        self.root.title("摸鱼背单词")
        normal_size = self.config['appearance']['window']['normal_size']
        window_pos = self.config['appearance']['window']['window_position']
        self.root.geometry(normal_size + window_pos)

        # 设置无边框和置顶
        self.root.overrideredirect(True)  # 无边框
        self.root.attributes('-topmost', True)  # 置顶

        # 设置背景色（你可以在这里修改背景颜色）
        background_color = self.config['appearance']['window']['background_color']
        self.root.configure(bg=background_color)

        print("窗口设置完成 - 背景色: {}".format(background_color))

    def setup_global_hotkeys(self):
        """设置全局快捷键"""
        if not PYNPUT_AVAILABLE:
            print("pynput库未安装，跳过全局快捷键设置")
            return

        # 检查配置中是否启用全局快捷键
        if not self.config.get('global_hotkeys', {}).get('enabled', False):
            print("全局快捷键已禁用（在配置文件中设置）")
            return

        try:
            from pynput.keyboard import GlobalHotKeys

            def on_toggle():
                """切换显示/隐藏"""
                self.root.after(0, self.toggle_visibility)

            def on_next():
                """下一个单词"""
                self.root.after(0, self.show_next_word)

            # 设置全局快捷键映射
            hotkey_map = {
                '<ctrl>+<shift>+w': on_toggle,
                '<ctrl>+<shift>+n': on_next
            }

            # 启动全局快捷键监听
            self.global_hotkeys = GlobalHotKeys(hotkey_map)
            self.global_hotkeys.start()

            print("全局快捷键已设置:")
            print("  Ctrl+Shift+W: 显示/隐藏窗口")
            print("  Ctrl+Shift+N: 下一个单词")

        except Exception as e:
            print("设置全局快捷键失败：{}".format(e))
            print("将使用窗口内快捷键")
        
    def load_words(self):
        """从txt文件加载单词"""
        self.words = []
        try:
            with open('IELTS阅读分类词汇.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 使用正则表达式匹配单词行
            word_pattern = r'^([a-zA-Z][a-zA-Z\s\-]*)\s+(n\.|adj\.|v\.|vt\.|vi\.|adv\.)'
            
            for i, line in enumerate(lines):
                line = line.strip()
                match = re.match(word_pattern, line)
                if match:
                    word = match.group(1).strip()
                    pos = match.group(2)  # 词性
                    # 提取中文释义
                    meaning = line[match.end():].strip()
                    
                    self.words.append({
                        'word': word,
                        'pos': pos,
                        'meaning': meaning,
                        'line_number': i + 1
                    })
                    
            print("成功加载 {} 个单词".format(len(self.words)))
            
        except FileNotFoundError:
            print("错误：找不到 IELTS阅读分类词汇.txt 文件")
            self.words = [{'word': 'error', 'pos': 'n.', 'meaning': '文件未找到', 'line_number': 0}]
        except Exception as e:
            print("加载单词时出错：{}".format(e))
            self.words = [{'word': 'error', 'pos': 'n.', 'meaning': '加载失败', 'line_number': 0}]
    
    def load_progress(self):
        """加载学习进度"""
        self.progress_file = 'word_progress.json'
        self.word_stats_file = 'word_stats.json'

        # 加载主进度文件
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    self.progress = json.load(f)
            else:
                self.progress = {
                    'current_index': 0,
                    'review_queue': [],
                    'last_review_date': None,
                    'total_learned': 0,
                    'new_words_since_review': 0,
                    'review_words_shown': 0,
                    'stats_summary': {
                        'familiar': 0,
                        'vague': 0,
                        'unfamiliar': 0
                    }
                }
        except Exception as e:
            print("加载主进度文件时出错：{}".format(e))
            self.progress = {
                'current_index': 0,
                'review_queue': [],
                'last_review_date': None,
                'total_learned': 0,
                'new_words_since_review': 0,
                'review_words_shown': 0,
                'stats_summary': {
                    'familiar': 0,
                    'vague': 0,
                    'unfamiliar': 0
                }
            }

        # 加载单词统计文件
        try:
            if os.path.exists(self.word_stats_file):
                with open(self.word_stats_file, 'r', encoding='utf-8') as f:
                    self.word_stats = json.load(f)
            else:
                self.word_stats = {}
        except Exception as e:
            print("加载单词统计文件时出错：{}".format(e))
            self.word_stats = {}

        # 迁移旧格式数据
        self.migrate_old_progress_format()

    def migrate_old_progress_format(self):
        """迁移旧的进度格式到新格式"""
        # 检查是否有旧格式的word_stats在主进度文件中
        old_word_stats = self.progress.get('word_stats', {})
        if old_word_stats:
            print("检测到旧格式数据，开始迁移...")

            # 迁移单词统计到独立文件
            for word, stats in old_word_stats.items():
                if 'familiarity_level' not in stats:
                    stats['familiarity_level'] = 0  # 默认为不熟悉
                if 'interval' not in stats:
                    stats['interval'] = 1  # 默认间隔1天
                if 'ease_factor' not in stats:
                    stats['ease_factor'] = 2.5  # 默认难度因子
                if 'next_review' not in stats:
                    # 如果有last_review，计算下次复习时间
                    if stats.get('last_review'):
                        last_review = datetime.fromisoformat(stats['last_review'])
                        next_review = last_review + timedelta(days=stats['interval'])
                        stats['next_review'] = next_review.isoformat()
                    else:
                        stats['next_review'] = datetime.now().isoformat()

                self.word_stats[word] = stats

            # 更新统计摘要
            self.update_stats_summary()

            # 从主进度文件中移除word_stats
            del self.progress['word_stats']

            # 保存迁移后的数据
            self.save_progress()
            self.save_word_stats()

            print("数据迁移完成，已分离单词统计到独立文件")

        # 确保有必要的字段
        if 'review_queue' not in self.progress:
            self.progress['review_queue'] = []
        if 'last_review_date' not in self.progress:
            self.progress['last_review_date'] = None
        if 'total_learned' not in self.progress:
            self.progress['total_learned'] = len(self.word_stats)
        if 'new_words_since_review' not in self.progress:
            self.progress['new_words_since_review'] = 0
        if 'review_words_shown' not in self.progress:
            self.progress['review_words_shown'] = 0
        if 'stats_summary' not in self.progress:
            self.update_stats_summary()

    def update_stats_summary(self):
        """更新统计摘要"""
        familiar = vague = unfamiliar = 0
        for stats in self.word_stats.values():
            level = stats.get('familiarity_level', 0)
            if level == 2:
                familiar += 1
            elif level == 1:
                vague += 1
            else:
                unfamiliar += 1

        self.progress['stats_summary'] = {
            'familiar': familiar,
            'vague': vague,
            'unfamiliar': unfamiliar
        }
        self.progress['total_learned'] = len(self.word_stats)

    def calculate_next_review(self, word, familiarity_level):
        """
        计算下次复习时间（间隔重复法）
        familiarity_level: 0=不认识, 1=有点模糊, 2=很熟悉
        """
        stats = self.word_stats.get(word, {})
        current_interval = stats.get('interval', 1)
        ease_factor = stats.get('ease_factor', 2.5)

        # 根据熟悉度调整间隔和难度因子
        if familiarity_level == 0:  # 不认识
            new_interval = 1  # 重新开始
            ease_factor = max(1.3, ease_factor - 0.2)  # 降低难度因子
        elif familiarity_level == 1:  # 有点模糊
            new_interval = max(1, int(current_interval * 0.6))  # 缩短间隔
            ease_factor = max(1.3, ease_factor - 0.15)  # 稍微降低难度因子
        else:  # 很熟悉 (familiarity_level == 2)
            if current_interval == 1:
                new_interval = 6  # 第一次熟悉后6天
            elif current_interval < 6:
                new_interval = 6
            else:
                new_interval = int(current_interval * ease_factor)  # 按难度因子增长
            ease_factor = min(2.5, ease_factor + 0.1)  # 提高难度因子

        # 限制间隔范围
        new_interval = max(1, min(365, new_interval))  # 最短1天，最长1年

        # 计算下次复习时间
        next_review = datetime.now() + timedelta(days=new_interval)

        return new_interval, ease_factor, next_review

    def update_word_familiarity(self, word, familiarity_level):
        """更新单词熟悉度并计算下次复习时间"""
        if word not in self.word_stats:
            self.word_stats[word] = {
                'review_count': 0,
                'first_seen': datetime.now().isoformat(),
                'familiarity_level': familiarity_level,
                'interval': 1,
                'ease_factor': 2.5
            }

        stats = self.word_stats[word]
        stats['review_count'] += 1
        stats['last_review'] = datetime.now().isoformat()
        stats['familiarity_level'] = familiarity_level

        # 计算下次复习时间
        new_interval, new_ease_factor, next_review = self.calculate_next_review(word, familiarity_level)
        stats['interval'] = new_interval
        stats['ease_factor'] = new_ease_factor
        stats['next_review'] = next_review.isoformat()

        # 更新统计摘要
        self.update_stats_summary()

        # 更新复习队列
        self.update_review_queue()

        # 保存数据
        self.save_progress()
        self.save_word_stats()

        # 打印反馈
        familiarity_names = ["不认识", "有点模糊", "很熟悉"]
        print("单词 '{}' 标记为: {} | 下次复习: {} 天后 ({})".format(
            word,
            familiarity_names[familiarity_level],
            new_interval,
            next_review.strftime("%Y-%m-%d")
        ))

    def update_review_queue(self):
        """更新复习队列，按下次复习时间排序"""
        now = datetime.now()
        review_queue = []

        for word, stats in self.word_stats.items():
            next_review_str = stats.get('next_review')
            if next_review_str:
                next_review = datetime.fromisoformat(next_review_str)
                if next_review <= now:  # 需要复习的单词
                    review_queue.append({
                        'word': word,
                        'next_review': next_review_str,
                        'priority': (now - next_review).total_seconds()  # 越久没复习优先级越高
                    })

        # 按优先级排序（优先级高的在前）
        review_queue.sort(key=lambda x: x['priority'], reverse=True)
        self.progress['review_queue'] = review_queue

        print("复习队列更新完成，待复习单词数: {}".format(len(review_queue)))

    def print_learning_stats(self):
        """打印学习统计信息"""
        total_words = len(self.words)
        learned_words = len(self.word_stats)

        # 从统计摘要获取数据
        stats_summary = self.progress.get('stats_summary', {'familiar': 0, 'vague': 0, 'unfamiliar': 0})
        familiar_count = stats_summary['familiar']
        vague_count = stats_summary['vague']
        unfamiliar_count = stats_summary['unfamiliar']

        # 更新复习队列以获取准确的待复习数量
        self.update_review_queue()
        review_count = len(self.progress['review_queue'])

        print("\n" + "="*50)
        print("📚 IELTS单词学习统计")
        print("="*50)
        print("总词汇量: {} 个".format(total_words))
        print("已学习: {} 个 ({:.1f}%)".format(learned_words, learned_words/total_words*100 if total_words > 0 else 0))
        print("  - 很熟悉: {} 个".format(familiar_count))
        print("  - 有点模糊: {} 个".format(vague_count))
        print("  - 不认识: {} 个".format(unfamiliar_count))
        print("待复习: {} 个".format(review_count))
        print("="*50)
        print("快捷键说明:")
        print("  1 - 标记为很熟悉")
        print("  2 - 标记为有点模糊")
        print("  3 - 标记为不认识")
        print("  N - 下一个单词")
        print("  Q - 退出程序")
        if self.ai_helper:
            print("  A - 获取AI详情")
            print("  S - 切换自动AI模式")
        print("="*50 + "\n")
    
    def save_progress(self):
        """保存学习进度（不包含单词统计）"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print("保存进度时出错：{}".format(e))

    def save_word_stats(self):
        """保存单词统计数据"""
        try:
            with open(self.word_stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.word_stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print("保存单词统计时出错：{}".format(e))
    
    def setup_ui(self):
        """设置用户界面"""
        # 从配置获取样式设置
        colors = self.config['appearance']['colors']
        fonts = self.config['appearance']['fonts']
        spacing = self.config['appearance']['spacing']

        # 主框架
        main_frame = tk.Frame(
            self.root,
            bg=colors['ui_background'],
            relief='solid',
            bd=spacing['border_width']
        )
        main_frame.pack(
            fill='both',
            expand=True,
            padx=spacing['margin_x'],
            pady=spacing['margin_y']
        )

        # 统一的字体设置
        font_config = (fonts['font_family'], fonts['font_size'], fonts['font_weight'])

        # 单词显示标签
        self.word_label = tk.Label(
            main_frame,
            text="",
            font=font_config,
            fg=colors['text_color'],
            bg=colors['text_background'],
            wraplength=self.config['word_display']['wrap_length'],
            relief='solid',
            bd=spacing['border_width'],
            padx=spacing['padding_x'],
            pady=spacing['padding_y']
        )
        self.word_label.pack(
            pady=(spacing['margin_y'], spacing['margin_y']),
            padx=spacing['margin_x'],
            fill='x'
        )

        # 词性和释义标签
        self.meaning_label = tk.Label(
            main_frame,
            text="",
            font=font_config,
            fg=colors['text_color'],
            bg=colors['text_background'],
            wraplength=self.config['word_display']['wrap_length'],
            justify='left',
            relief='solid',
            bd=spacing['border_width'],
            padx=spacing['padding_x'],
            pady=spacing['padding_y']
        )
        self.meaning_label.pack(
            pady=(0, spacing['margin_y']),
            padx=spacing['margin_x'],
            fill='x'
        )

        # AI详情显示区域（初始隐藏）
        self.ai_details_frame = tk.Frame(
            main_frame,
            bg=colors['ui_background']
        )

        self.ai_details_label = tk.Label(
            self.ai_details_frame,
            text="",
            font=font_config,  # 使用统一的字体配置
            fg=colors['text_color'],
            bg=colors['text_background'],
            wraplength=self.config['word_display']['wrap_length'],
            justify='left',
            relief='solid',
            bd=spacing['border_width'],
            padx=spacing['padding_x'],
            pady=spacing['padding_y']
        )
        self.ai_details_label.pack(
            pady=spacing['margin_y'],
            padx=spacing['margin_x'],
            fill='x'
        )

        # 绑定键盘事件到所有组件
        self.root.bind('<Key>', self.on_key_press)
        self.root.bind('<KeyPress>', self.on_key_press)
        main_frame.bind('<Key>', self.on_key_press)
        main_frame.bind('<KeyPress>', self.on_key_press)
        self.word_label.bind('<Key>', self.on_key_press)
        self.word_label.bind('<KeyPress>', self.on_key_press)
        self.meaning_label.bind('<Key>', self.on_key_press)
        self.meaning_label.bind('<KeyPress>', self.on_key_press)

        # 设置焦点和可聚焦属性
        self.root.focus_set()
        main_frame.focus_set()
        main_frame.config(takefocus=True)
        self.word_label.config(takefocus=True)
        self.meaning_label.config(takefocus=True)

        # 绑定鼠标事件用于拖拽窗口
        self.root.bind('<Button-1>', self.start_drag)
        self.root.bind('<B1-Motion>', self.on_drag)

        # 绑定鼠标点击事件来确保焦点
        def set_focus(event):
            self.root.focus_set()

        self.root.bind('<Button-1>', lambda e: (self.start_drag(e), set_focus(e)))
        main_frame.bind('<Button-1>', set_focus)
        self.word_label.bind('<Button-1>', set_focus)
        self.meaning_label.bind('<Button-1>', set_focus)
        
    def start_drag(self, event):
        """开始拖拽窗口"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        
    def on_drag(self, event):
        """拖拽窗口"""
        x = self.root.winfo_x() + event.x - self.drag_start_x
        y = self.root.winfo_y() + event.y - self.drag_start_y
        self.root.geometry("+{}+{}".format(x, y))
    
    def toggle_visibility(self):
        """切换窗口显示/隐藏"""
        if self.is_visible:
            self.root.withdraw()  # 隐藏窗口
            self.is_visible = False
            print("窗口已隐藏")
        else:
            self.root.deiconify()  # 显示窗口
            self.root.lift()  # 提升到前台
            self.root.attributes('-topmost', True)  # 确保置顶
            self.is_visible = True
            print("窗口已显示")

    def on_key_press(self, event):
        """处理键盘事件"""
        key = event.keysym.lower()
        hotkeys = self.config['hotkeys']

        if key == hotkeys['next_word']:
            self.show_next_word()
        elif key == hotkeys['quit_app'] or key == hotkeys['quit_app_alt'].lower():
            self.close_app()
        elif key == hotkeys['toggle_visibility']:
            self.toggle_visibility()
        elif key == hotkeys.get('get_ai_details', 'a'):
            self.get_ai_details()
        elif key == hotkeys.get('toggle_ai_details', 'd'):
            self.toggle_ai_details_display()
        elif key == hotkeys.get('toggle_auto_ai', 's'):
            self.toggle_auto_ai()
        elif key == hotkeys.get('familiar', '1'):
            self.rate_word_familiarity(2)  # 很熟悉
        elif key == hotkeys.get('vague', '2'):
            self.rate_word_familiarity(1)  # 有点模糊
        elif key == hotkeys.get('unfamiliar', '3'):
            self.rate_word_familiarity(0)  # 不认识
        elif key == hotkeys.get('show_details', 'j'):
            self.show_word_details()  # 显示详细信息

    def rate_word_familiarity(self, familiarity_level):
        """评级当前单词的熟悉度并跳转到下一个单词"""
        if not self.current_word_info:
            print("没有当前单词可以评级")
            return

        word = self.current_word_info['word']
        self.update_word_familiarity(word, familiarity_level)

        # 自动跳转到下一个单词
        self.show_next_word()

    def show_word_details(self):
        """显示当前单词的详细信息（词性+释义）"""
        if not self.current_word_info:
            return

        if self.details_shown:
            print("详细信息已经显示")
            return

        # 更新显示为完整信息
        word_info = self.current_word_info
        combined_text = "单词: {} 词性: {} 释义: {}".format(
            word_info['word'],
            word_info['pos'],
            word_info['meaning']
        )
        self.word_label.config(text=combined_text)
        self.details_shown = True
        print("已显示详细信息")

    def get_key_function(self, key):
        """获取按键对应的功能描述"""
        hotkeys = self.config['hotkeys']
        if key == hotkeys['next_word']:
            return "下一个单词"
        elif key == hotkeys['quit_app'] or key == hotkeys['quit_app_alt'].lower():
            return "退出程序"
        elif key == hotkeys['toggle_visibility']:
            return "显示/隐藏窗口"
        elif key == hotkeys.get('get_ai_details', 'a'):
            return "获取AI详情"
        elif key == hotkeys.get('toggle_ai_details', 'd'):
            return "显示/隐藏AI详情"
        elif key == hotkeys.get('toggle_auto_ai', 's'):
            return "切换自动AI模式"
        elif key == hotkeys.get('familiar', '1'):
            return "标记为很熟悉"
        elif key == hotkeys.get('vague', '2'):
            return "标记为有点模糊"
        elif key == hotkeys.get('unfamiliar', '3'):
            return "标记为不认识"
        else:
            return "未定义"

    def get_ai_details(self):
        """获取当前单词的AI详情"""
        if not self.ai_helper:
            print("AI助手不可用")
            return

        if not self.current_word_info:
            print("没有当前单词")
            return

        word = self.current_word_info['word']
        pos = self.current_word_info['pos']
        meaning = self.current_word_info['meaning']

        print("正在获取单词 '{}' 的AI详情...".format(word))

        # 在后台线程中获取AI详情，避免阻塞UI
        def fetch_ai_details():
            try:
                details = self.ai_helper.get_word_details(word, pos, meaning)
                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_ai_details(details))
            except Exception as e:
                print("获取AI详情失败: {}".format(e))
                self.root.after(0, lambda: self.update_ai_details(None))

        import threading
        thread = threading.Thread(target=fetch_ai_details)
        thread.daemon = True
        thread.start()

    def update_ai_details(self, details):
        """更新AI详情显示"""
        if details:
            self.current_ai_details = details

            print("🖼️ 开始更新UI显示...")
            print("详情内容: {}".format(details))

            # 直接显示AI的原始回复内容，移除换行符
            raw_content = details.get('raw_content', '暂无详细信息')

            # 移除换行符，显示为连续文本
            display_text = raw_content.replace('\n', ' ').replace('\\n', ' ').strip()
            # 清理多余的空格
            display_text = ' '.join(display_text.split())

            print("直接显示AI原始回复: {}".format(display_text))

            # 更新标签内容
            self.ai_details_label.config(text=display_text)
            print("UI标签已更新")

            # 自动显示AI详情
            if not self.ai_details_visible:
                self.show_ai_details()

            print("✅ AI详情获取成功并已显示")
        else:
            self.ai_details_label.config(text="获取AI详情失败，请稍后重试")
            if not self.ai_details_visible:
                self.show_ai_details()
            print("❌ AI详情获取失败")

    def toggle_ai_details_display(self):
        """切换AI详情显示/隐藏"""
        if self.ai_details_visible:
            self.hide_ai_details()
        else:
            self.show_ai_details()

    def show_ai_details(self):
        """显示AI详情"""
        if not self.ai_details_visible:
            self.ai_details_frame.pack(
                pady=(0, self.config['appearance']['spacing']['margin_y']),
                padx=self.config['appearance']['spacing']['margin_x'],
                fill='x'
            )
            self.ai_details_visible = True

            # 动态调整窗口大小以适应AI内容
            self.adjust_window_size()
            print("AI详情已显示，窗口大小已调整")

    def hide_ai_details(self):
        """隐藏AI详情"""
        if self.ai_details_visible:
            self.ai_details_frame.pack_forget()
            self.ai_details_visible = False

            # 恢复原始窗口大小
            self.restore_window_size()
            print("AI详情已隐藏，窗口大小已恢复")

    def toggle_auto_ai(self):
        """切换自动AI模式"""
        if not self.ai_helper:
            print("AI助手不可用，无法切换自动AI模式")
            return

        self.auto_ai_enabled = not self.auto_ai_enabled

        # 更新配置文件中的设置
        self.config['ai_helper']['auto_fetch'] = self.auto_ai_enabled
        self.save_config()

        if self.auto_ai_enabled:
            print("✅ 自动AI模式已启用 - 每次显示新单词时自动获取AI详情")
            print("配置已保存，下次启动时将保持此设置")
            # 如果当前有单词，立即获取AI详情
            if self.current_word_info:
                self.get_ai_details()
        else:
            print("❌ 自动AI模式已禁用 - 需要手动按 'A' 键获取AI详情")
            print("配置已保存，下次启动时将保持此设置")
            # 隐藏当前的AI详情
            if self.ai_details_visible:
                self.hide_ai_details()

    def save_config(self):
        """保存配置文件"""
        try:
            with open('word_config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print("配置文件已保存")
        except Exception as e:
            print("保存配置文件失败: {}".format(e))

    def adjust_window_size(self):
        """调整窗口大小以显示AI内容"""
        try:
            # 获取当前窗口位置
            current_geometry = self.root.geometry()
            parts = current_geometry.split('+')
            if len(parts) >= 3:
                position = '+' + parts[1] + '+' + parts[2]
            else:
                position = self.config['appearance']['window']['window_position']

            # 使用配置的AI模式窗口大小
            ai_window_size = self.config['appearance']['window']['ai_mode_size']
            new_geometry = "{}{}".format(ai_window_size, position)
            self.root.geometry(new_geometry)

            print("窗口大小已调整为AI模式: {}".format(new_geometry))

        except Exception as e:
            print("调整窗口大小失败: {}".format(e))

    def restore_window_size(self):
        """恢复原始窗口大小"""
        try:
            # 获取当前窗口位置
            current_geometry = self.root.geometry()
            parts = current_geometry.split('+')
            if len(parts) >= 3:
                position = '+' + parts[1] + '+' + parts[2]
            else:
                position = self.config['appearance']['window']['window_position']

            # 恢复到配置文件中的普通大小
            normal_size = self.config['appearance']['window']['normal_size']
            new_geometry = "{}{}".format(normal_size, position)
            self.root.geometry(new_geometry)

            print("窗口大小已恢复为: {}".format(new_geometry))

        except Exception as e:
            print("恢复窗口大小失败: {}".format(e))
    
    def show_next_word(self):
        """显示下一个单词（基于数量的重复逻辑）"""
        if not self.words:
            return

        # 基于数量的复习逻辑
        word_info = self.get_next_word_by_count()

        # 设置当前单词信息
        self.current_word_info = word_info

        # 重置详细信息显示状态
        self.details_shown = False

        # 初始只显示单词
        word_text = "单词: {}".format(word_info['word'])
        self.word_label.config(text=word_text)
        # 隐藏原来的释义标签
        self.meaning_label.config(text="")

        # 显示单词的学习状态
        word = word_info['word']
        stats = self.word_stats.get(word, {})
        familiarity_level = stats.get('familiarity_level', -1)
        review_count = stats.get('review_count', 0)

        if familiarity_level >= 0:
            familiarity_names = ["不认识", "有点模糊", "很熟悉"]
            status_text = "状态: {} | 复习次数: {}".format(
                familiarity_names[familiarity_level], review_count)
        else:
            status_text = "状态: 新单词 | 请按 1(熟悉) 2(模糊) 3(不认识) 评级"

        print(status_text)

        # 保存进度
        self.save_progress()

        # 隐藏之前的AI详情
        if self.ai_details_visible:
            self.hide_ai_details()

        # 如果启用了自动AI模式，自动获取AI详情
        if self.auto_ai_enabled and self.ai_helper:
            print("自动AI模式已启用，正在获取AI详情...")
            self.get_ai_details()

    def get_next_word_by_count(self):
        """基于数量的单词选择逻辑"""
        # 配置参数
        NEW_WORDS_BEFORE_REVIEW = 5  # 每学5个新单词后复习旧单词
        REVIEW_WORDS_COUNT = 2       # 每次复习2个旧单词

        # 获取当前学习计数
        new_words_learned = self.progress.get('new_words_since_review', 0)
        review_words_shown = self.progress.get('review_words_shown', 0)

        # 判断是否需要复习旧单词
        if new_words_learned >= NEW_WORDS_BEFORE_REVIEW and review_words_shown < REVIEW_WORDS_COUNT:
            # 需要复习旧单词
            review_word = self.get_review_word()
            if review_word:
                self.progress['review_words_shown'] = review_words_shown + 1
                print("复习模式: 显示需要复习的单词 '{}'".format(review_word['word']))
                return review_word

        # 如果复习完成，重置计数器
        if review_words_shown >= REVIEW_WORDS_COUNT:
            self.progress['new_words_since_review'] = 0
            self.progress['review_words_shown'] = 0

        # 显示新单词
        current_index = self.progress.get('current_index', 0)

        # 确保索引在有效范围内
        if current_index >= len(self.words):
            current_index = 0

        word_info = self.words[current_index]
        self.progress['current_index'] = (current_index + 1) % len(self.words)

        # 如果这是一个新单词（之前没学过），增加新单词计数
        if word_info['word'] not in self.word_stats:
            self.progress['new_words_since_review'] = new_words_learned + 1
            print("学习模式: 显示新单词 '{}' ({}/{})".format(
                word_info['word'], current_index + 1, len(self.words)))
        else:
            print("学习模式: 显示单词 '{}' ({}/{})".format(
                word_info['word'], current_index + 1, len(self.words)))

        return word_info

    def get_review_word(self):
        """获取需要复习的单词"""
        # 获取所有学过的单词，按熟悉度排序（不熟悉的优先）
        learned_words = []
        for word, stats in self.word_stats.items():
            familiarity = stats.get('familiarity_level', 0)
            review_count = stats.get('review_count', 0)
            learned_words.append({
                'word': word,
                'familiarity': familiarity,
                'review_count': review_count
            })

        if not learned_words:
            return None

        # 按熟悉度排序（不熟悉的在前），然后按复习次数排序（复习少的在前）
        learned_words.sort(key=lambda x: (x['familiarity'], x['review_count']))

        # 选择最需要复习的单词
        review_word_name = learned_words[0]['word']

        # 在单词列表中找到对应的单词信息
        for word in self.words:
            if word['word'] == review_word_name:
                return word

        return None
    
    def toggle_transparency_mode(self):
        """切换透明度模式"""
        if self.transparency_mode == 'transparent':
            # 切换到半透明模式
            self.transparency_mode = 'semi'
            self.current_alpha_index = 2  # 0.7透明度
            try:
                self.root.wm_attributes('-transparentcolor', '')  # 清除透明色
            except:
                pass
            self.root.attributes('-alpha', self.alpha_levels[self.current_alpha_index])
            print("切换到半透明模式 (透明度: {})".format(self.alpha_levels[self.current_alpha_index]))
        else:
            # 切换到透明背景模式
            self.transparency_mode = 'transparent'
            try:
                self.root.wm_attributes('-transparentcolor', self.transparent_color)
                self.root.attributes('-alpha', 1.0)
                print("切换到透明背景模式")
            except:
                self.root.attributes('-alpha', 0.95)
                print("透明背景不支持，使用高透明度")

    def increase_transparency(self):
        """增加透明度（更透明）"""
        if self.transparency_mode == 'transparent':
            print("已经是最高透明度（透明背景模式）")
            return

        if self.current_alpha_index > 0:
            self.current_alpha_index -= 1
            self.root.attributes('-alpha', self.alpha_levels[self.current_alpha_index])
            print("增加透明度: {}".format(self.alpha_levels[self.current_alpha_index]))
        else:
            # 切换到透明背景模式
            self.transparency_mode = 'transparent'
            try:
                self.root.wm_attributes('-transparentcolor', self.transparent_color)
                self.root.attributes('-alpha', 1.0)
                print("切换到最高透明度（透明背景模式）")
            except:
                print("无法设置透明背景")

    def decrease_transparency(self):
        """减少透明度（更不透明）"""
        if self.transparency_mode == 'transparent':
            # 从透明背景模式切换到半透明
            self.transparency_mode = 'semi'
            self.current_alpha_index = 0  # 最透明的半透明级别
            try:
                self.root.wm_attributes('-transparentcolor', '')
            except:
                pass
            self.root.attributes('-alpha', self.alpha_levels[self.current_alpha_index])
            print("切换到半透明模式: {}".format(self.alpha_levels[self.current_alpha_index]))
        else:
            if self.current_alpha_index < len(self.alpha_levels) - 1:
                self.current_alpha_index += 1
                self.root.attributes('-alpha', self.alpha_levels[self.current_alpha_index])
                print("减少透明度: {}".format(self.alpha_levels[self.current_alpha_index]))
            else:
                print("已经是最低透明度")

    def switch_transparent_color(self):
        """切换透明色，测试不同效果"""
        if self.transparency_mode == 'transparent':
            self.current_transparent_color_index = (self.current_transparent_color_index + 1) % len(self.transparent_colors)
            self.transparent_color = self.transparent_colors[self.current_transparent_color_index]
            self.root.configure(bg=self.transparent_color)
            try:
                self.root.wm_attributes('-transparentcolor', self.transparent_color)
                print("切换透明色: {} (测试文字清晰度)".format(self.transparent_color))
            except:
                print("切换透明色失败")
        else:
            print("请先切换到透明背景模式")

    def close_app(self):
        """关闭应用"""
        self.save_progress()

        # 清理全局快捷键
        if PYNPUT_AVAILABLE and hasattr(self, 'global_hotkeys'):
            try:
                self.global_hotkeys.stop()
                print("全局快捷键已清理")
            except:
                pass

        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.close_app()

if __name__ == "__main__":
    app = WordFlashcard()
    app.run()
