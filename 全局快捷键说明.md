# 全局快捷键使用说明

## 当前状态
全局快捷键功能已默认**禁用**，程序使用窗口内快捷键。

## 启用全局快捷键

### 1. 确保pynput库已安装
```bash
pip install pynput
```

### 2. 修改配置文件
编辑 `word_config.json`，将 `global_hotkeys.enabled` 改为 `true`：

```json
{
  "global_hotkeys": {
    "enabled": true,
    "toggle_visibility": "ctrl+shift+w",
    "next_word": "ctrl+shift+n"
  }
}
```

### 3. 重启程序
重新运行 `python word_flashcard.py`

## 全局快捷键功能

启用后可以在任何应用中使用：
- **Ctrl+Shift+W**: 显示/隐藏单词窗口
- **Ctrl+Shift+N**: 切换到下一个单词

## 窗口内快捷键（始终可用）

无论是否启用全局快捷键，以下快捷键在窗口获得焦点时始终可用：
- **N**: 下一个单词
- **H**: 隐藏/显示窗口
- **Q**: 退出程序

## 故障排除

1. **全局快捷键不工作**：
   - 检查pynput库是否正确安装
   - 确认配置文件中 `enabled` 为 `true`
   - 重启程序

2. **快捷键冲突**：
   - 修改配置文件中的快捷键组合
   - 避免与系统或其他软件的快捷键冲突

3. **权限问题**：
   - 某些系统可能需要管理员权限才能使用全局快捷键
   - 尝试以管理员身份运行程序

## 推荐设置

对于日常使用，建议：
- 保持全局快捷键禁用，使用窗口内快捷键
- 需要时可以临时启用全局快捷键功能
- 这样可以避免潜在的权限和冲突问题
